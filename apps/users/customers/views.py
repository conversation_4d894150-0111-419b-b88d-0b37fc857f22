from rest_framework import viewsets
from rest_framework import permissions
from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework_simplejwt.tokens import RefreshToken
from .serializers import (
    CustomerSerializer,
    PhoneTokenSerializer,
    CustomerCreateSerializer,
    PhoneTokenResendSerializer,
    ResetPasswordSerializer,
    ForgotPasswordSerializer
)
from apps.users.models import Customer, PhoneToken, User, UserTypes
from apps.users.vendors.models import VendorClient
from apps.users.vendors.serializers import VendorClientSerializer
from core.sms import eskiz
from apps.users.customers.utils import generate_token, verify_token
from apps.orders.models import Order
from apps.orders.serializers import OrderSerializer
from rest_framework import serializers



class CustomerViewSet(viewsets.ModelViewSet):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    http_method_names = ['get', 'post', 'patch']

    def get_queryset(self):
        return self.queryset

    def get_serializer_class(self):
        if self.action == "create":
            return CustomerCreateSerializer
        elif self.action == "verify":
            return PhoneTokenSerializer
        elif self.action == "resend":
            return PhoneTokenResendSerializer
        elif self.action == "reset_password":
            return ResetPasswordSerializer
        elif self.action == "partial_update":
            return CustomerCreateSerializer
        elif self.action == "forgot_password":
            return ForgotPasswordSerializer
        return CustomerSerializer

    def create(self, request, *args, **kwargs):
        """
        Create a new customer with improved error handling
        """
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            return Response(
                {
                    "status": "success",
                    "message": "Customer account created successfully",
                    "data": serializer.data
                },
                status=status.HTTP_201_CREATED
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while creating the account",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def verify(self, request, *args, **kwargs):
        serializer = PhoneTokenSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        phone_number = serializer.validated_data.get("phone_number")
        token = serializer.validated_data.get("token")
        if verify_token(phone_number, token):
            user = User.objects.get(phone_number=phone_number)
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "role": user.user_type,
                }
            )
        return Response(
            {"status": "error", "message": "Phone number is not verified"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    
    @action(detail=False, methods=["get"])
    def track_latest_order_status(self, request):
        user = request.user
        if user.is_authenticated:
            if user.user_type == UserTypes.CUSTOMER:
                customer = user.customer
                latest_order = Order.objects.filter(customer=customer).order_by('-created_at').first()
                if latest_order:
                    return Response({
                        "order_id": latest_order.id,
                        "status": latest_order.status,
                        "product": latest_order.product.title if latest_order.product else None,
                        "quantity": latest_order.quantity,
                        "delivered_at": latest_order.delivered_at,
                    }, status=status.HTTP_200_OK)
                return Response({"message": "No orders found"}, status=status.HTTP_404_NOT_FOUND)
            return Response(status=status.HTTP_403_FORBIDDEN)
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    @action(detail=False, methods=["get"])
    def order_history(self, request, *args, **kwargs):
        """
        Get customer's order history with improved error handling
        """
        try:
            user = request.user
            if not user.is_authenticated:
                return Response(
                    {
                        "status": "error",
                        "message": "Authentication required"
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )

            if user.user_type != UserTypes.CUSTOMER:
                return Response(
                    {
                        "status": "error",
                        "message": "Only customers can access their order history"
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            customer = user.customer
            orders = Order.objects.filter(customer=customer).order_by('-created_at')
            page = self.paginate_queryset(orders)
            
            if page is not None:
                serializer = OrderSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = OrderSerializer(orders, many=True)
            return Response(
                {
                    "status": "success",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while fetching order history",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def resend(self, request, *args, **kwargs):
        """
        Resend verification token with improved error handling
        """
        try:
            serializer = PhoneTokenSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            phone_number = serializer.validated_data.get("phone_number")
            token = generate_token(phone_number)
            
            if token:
                return Response(
                    {
                        "status": "success",
                        "message": "Verification code has been resent"
                    },
                    status=status.HTTP_200_OK
                )
            
            return Response(
                {
                    "status": "error",
                    "message": "Failed to send verification code"
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid phone number provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while sending verification code",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=["post"])
    def forgot_password(self, request, *args, **kwargs):
        serializer = ForgotPasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        phone_number = serializer.validated_data.get("phone_number")
        password = serializer.validated_data.get("password")
        confirm_password = serializer.validated_data.get("confirm_password")
        user = User.objects.get(phone_number=phone_number)
        phone_token = PhoneToken.objects.filter(phone_number=phone_number).last()

        if not phone_token:
            return Response({"status": "Verify your phone"}, status=status.HTTP_400_BAD_REQUEST)

        if not verify_token(phone_number, phone_token.token):
            return Response({"status": "Verify your phone"}, status=status.HTTP_400_BAD_REQUEST)

        if user.user_type != UserTypes.CUSTOMER:
            return Response({"status": "error"}, status=status.HTTP_400_BAD_REQUEST)

        if password == confirm_password:
            user.set_password(password)
            user.save()
            return Response({"status": "success"})
        return Response({"status": "error"}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=["get"])
    def me(self, request, *args, **kwargs):
        user = request.user
        if user.is_authenticated:
            if user.user_type == UserTypes.CUSTOMER:
                vendor_obj = user.customer
                serializer = CustomerSerializer(vendor_obj)
                data = serializer.data
                return Response(data, status=status.HTTP_200_OK)
            return Response(status=status.HTTP_403_FORBIDDEN)
        return Response(status=status.HTTP_401_UNAUTHORIZED)
    
    @action(detail=False, methods=["post"])
    def reset_password(self, request):
        """
        Reset password with improved error handling
        """
        try:
            user = request.user
            if not user.is_authenticated:
                return Response(
                    {
                        "status": "error",
                        "message": "Authentication required"
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )

            if user.user_type != UserTypes.CUSTOMER:
                return Response(
                    {
                        "status": "error",
                        "message": "Only customers can reset their password"
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            serializer = ResetPasswordSerializer(data=request.data, context={'request': request})
            serializer.is_valid(raise_exception=True)
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            
            return Response(
                {
                    "status": "success",
                    "message": "Password has been reset successfully"
                },
                status=status.HTTP_200_OK
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid password data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while resetting password",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def vendors(self, request, *args, **kwargs):
        """
        Get customer's vendors with improved error handling
        """
        try:
            user = request.user
            if not user.is_authenticated:
                return Response(
                    {
                        "status": "error",
                        "message": "Authentication required"
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )

            if user.user_type != UserTypes.CUSTOMER:
                return Response(
                    {
                        "status": "error",
                        "message": "Only customers can access their vendors"
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            customer = user.customer
            clients = VendorClient.objects.filter(customer=customer)
            serializer = VendorClientSerializer(clients, many=True)
            pagination = self.paginate_queryset(serializer.data)
            
            if pagination:
                return self.get_paginated_response(pagination)
            
            return Response(
                {
                    "status": "success",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while fetching vendors",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
