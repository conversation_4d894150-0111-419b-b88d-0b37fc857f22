"""
customers serializers
"""
from django.core.files.storage import default_storage
from django.db.models import Sum
from rest_framework import serializers

from apps.users.common.utils import create_username
from apps.users.models import Customer, PhoneToken, User, UserTypes
from apps.users.vendors.models import VendorClient


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for the User model.
    Handles user creation with optional password field.
    """
    password = serializers.CharField(write_only=True, required=False)

    class Meta:
        """
        the meta class
        """
        model = User
        fields = ("id", "phone_number", "user_type", "password")
        read_only_fields = ("user_type",)

    def create(self, validated_data):
        """
        Create and return a new user using the custom create_user method.
        """
        return User.objects.create_user(**validated_data)


class CustomerSerializer(serializers.ModelSerializer):
    """
    Serializer for retrieving Customer data.
    Includes nested User data and return bottle count from VendorClient.
    """
    user = UserSerializer()
    return_bottles = serializers.SerializerMethodField()

    class Meta:
        """
        the meta class
        """
        model = Customer
        fields = (
            "id",
            "user",
            "fullname",
            "avatar",
            "number1",
            "number2",
            "longitude",
            "latitude",
            "passport",
            "region",
            "district",
            "user_type",
            "return_bottles",
        )
        read_only_fields = ("user_type",)

    def get_return_bottles(self, obj):
        """
        Return total return_bottles aggregated from VendorClient linked to the customer.
        """
        total = VendorClient.objects.filter(customer=obj).aggregate(
            total=Sum("return_bottles")
        )["total"] or 0
        return total


class CustomerCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating Customer instances.
    Includes nested user creation and avatar file handling.
    """
    user = UserSerializer()

    class Meta:
        """
        the meta class
        """
        model = Customer
        fields = (
            "id",
            "user",
            "fullname",
            "avatar",
            "number1",
            "number2",
            "longitude",
            "latitude",
            "passport",
            "region",
            "district",
            "user_type",
        )
        read_only_fields = ("user_type", "number1")

    def create(self, validated_data):
        """
        Create a new Customer along with a nested User.
        Assigns default username and sets user_type to CUSTOMER.
        """
        user_data = validated_data.pop("user")
        username = create_username()
        user_data["username"] = username
        user = User.objects.create_user(
            **user_data,
            user_type=UserTypes.CUSTOMER,
            is_active=False
        )
        return Customer.objects.create(user=user, **validated_data)

    def update(self, instance, validated_data):
        """
        Update an existing Customer.
        Restricts access to the customer themselves if the request user is of type CUSTOMER.
        Handles avatar file upload via default storage (e.g., S3).
        """
        request_user = self.context.get("request").user

        if request_user.user_type == UserTypes.CUSTOMER:
            if request_user.customer != instance:
                raise serializers.ValidationError(
                    {"detail": "You don't have permission to perform this action."}
                )

        avatar = validated_data.pop("avatar", None)
        if avatar:
            try:
                file_name = default_storage.save(f"customers/{avatar.name}", avatar)
                file_url = default_storage.url(file_name)
                validated_data["avatar"] = file_url
            except Exception as e:
                raise serializers.ValidationError(
                    {"avatar": f"File upload failed: {str(e)}"}
                )

        return super().update(instance, validated_data)


class PhoneTokenSerializer(serializers.ModelSerializer):
    """
    Serializer for reading PhoneToken model, typically used for verification.
    """
    class Meta:
        """
        the meta class
        """
        model = PhoneToken
        fields = ["phone_number", "token"]


class PhoneTokenResendSerializer(serializers.ModelSerializer):
    """
    Serializer for resending phone tokens based on phone number.
    """
    class Meta:
        """
        the meta class
        """
        model = PhoneToken
        fields = ["phone_number"]


class ResetPasswordSerializer(serializers.Serializer):
    """
    Serializer to handle password reset by an authenticated user.
    Validates old password and ensures new passwords match.
    """
    old_password = serializers.CharField()
    new_password = serializers.CharField()
    confirm_password = serializers.CharField()

    def validate(self, data):
        """
        Validate that old password is correct and new passwords match.
        """
        user = self.context["request"].user
        old_password = data["old_password"]

        if not user.check_password(old_password):
            raise serializers.ValidationError({"old_password": "Wrong password."})

        if data["new_password"] != data["confirm_password"]:
            raise serializers.ValidationError("Passwords don't match")

        return data


class ForgotPasswordSerializer(serializers.Serializer):
    """
    Serializer to handle password reset via phone number.
    Ensures user exists and passwords match.
    """
    phone_number = serializers.CharField()
    password = serializers.CharField()
    confirm_password = serializers.CharField()

    def validate(self, data):
        """
        Validate that passwords match and the user exists with the given phone number.
        """
        if data["password"] != data["confirm_password"]:
            raise serializers.ValidationError("Passwords don't match")

        phone_number = data["phone_number"]
        if not User.objects.filter(phone_number=phone_number).exists():
            raise serializers.ValidationError(
                {"phone_number": "Phone number is not registered"}
            )

        return data
