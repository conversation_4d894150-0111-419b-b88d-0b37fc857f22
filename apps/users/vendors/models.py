"""
Vendor models
"""
from django.db import models
from django.core.exceptions import ValidationError

from smart_selects.db_fields import ChainedManyToManyField

from core.base_models import BaseModel
from apps.users.models import Vendor, Customer
from apps.helpers.models import Region, District
from apps.users.validators import is_valid_uzb_phone_number


class RequestVendor(BaseModel):
    """
    Request vendor model
    """
    name = models.CharField(max_length=255)
    phone_number = models.CharField(
        max_length=255, validators=[is_valid_uzb_phone_number]
    )
    description = models.TextField()

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Requested vendor"
        verbose_name_plural = "Requested vendors"
        ordering = ("-created_at",)


class VendorAvailablePlaces(models.Model):
    """
    Vendor available places model
    """
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name="places_available")
    region = models.ForeignKey(Region, on_delete=models.CASCADE)
    districts = ChainedManyToManyField(
        District,
        chained_field="region",
        chained_model_field="region",
        auto_choose=True
    )

    def __str__(self):
        return f"{self.vendor.name} - {self.region}"

    def clean(self):
        existing_places = VendorAvailablePlaces.objects.filter(
            vendor=self.vendor,
            region=self.region
        )
        if self.pk:
            existing_places = existing_places.exclude(pk=self.pk)

        for place in existing_places:
            existing_district_ids = set(place.districts.values_list('id', flat=True))
            new_district_ids = set(self.districts.values_list('id', flat=True))

            if existing_district_ids == new_district_ids:
                raise ValidationError("This combination of vendor, region and districts already exists.")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)


class VendorClient(BaseModel):
    """
    Vendor client model
    """
    phone_number = models.CharField(
        max_length=255, validators=[is_valid_uzb_phone_number],
        null=True,
        blank=True
    )
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name="clients")
    customer = models.ForeignKey(Customer, on_delete=models.SET_NULL,
                                 related_name="vendors", null=True, blank=True)
    fullname = models.CharField(max_length=100, null=True, blank=True)
    passport = models.CharField(max_length=12, null=True, blank=True)
    return_bottles = models.IntegerField(default=0)
    orders_count = models.IntegerField(default=0)
    additional_number = models.CharField(
        max_length=255, validators=[is_valid_uzb_phone_number], null=True, blank=True
    )
    address = models.CharField(max_length=255)
    region = models.ForeignKey(Region, on_delete=models.SET_NULL, null=True, blank=True)
    district = models.ForeignKey(District, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        if self.customer:
            return f"{self.customer.fullname}"
        return self.phone_number


class DamageReport(BaseModel):
    """
    Model to store damage reports
    """
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name="damage_reports")
    user = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name="damage_reports")
    damaged_bottles = models.IntegerField()
    is_covered = models.BooleanField()
    datetime = models.DateTimeField()

    def __str__(self):
        return f"Damage Report: {self.vendor.name} - {self.damaged_bottles} bottles"


class VendorStats(BaseModel):
    """
    Model for storing vendor statistics
    """
    vendor = models.OneToOneField(
        Vendor,
        on_delete=models.CASCADE,
        related_name="stats"
    )
    total_bottles = models.IntegerField(default=0)
    ordered_bottles = models.IntegerField(default=0)
    available_bottles = models.IntegerField(default=0)
    customer_count = models.IntegerField(default=0)
    weekly_turnover = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    class Meta:
        verbose_name = "Vendor Statistics"
        verbose_name_plural = "Vendor Statistics"
        db_table = "vendor_stats"

    def __str__(self):
        return f"Stats for {self.vendor.name}"
