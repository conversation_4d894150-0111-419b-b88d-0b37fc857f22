import random
import string
import uuid

from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from django.utils.safestring import mark_safe
from django.core.validators import FileExtensionValidator
from apps.users.common.validators import is_valid_uzb_phone_number
from apps.users.managers import CustomUserManager, VerifiedVendorManager, CourierManager
from core.base_models import BaseModel


class UserTypes(models.TextChoices):
    """
    User role types
    """
    ADMIN = "ADMIN", "ADMIN"
    VENDOR = "VENDOR", "VENDOR"
    CUSTOMER = "CUSTOMER", "CUSTOMER"
    COURIER = "COURIER", "COURIER"


class User(AbstractUser):
    """
    the user model
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    phone_number = models.CharField(
        max_length=15,
        null=True,
        blank=True,
        validators=[is_valid_uzb_phone_number],
        error_messages={
            "unique": "A user with that number already exists.",
        },
    )
    chat_id = models.BigIntegerField(
        null=True,
        blank=True,
        unique=True,
        help_text="Telegram chat ID for bot communication"
    )
    user_type = models.CharField(
        max_length=20, choices=UserTypes.choices, default=UserTypes.CUSTOMER
    )
    fcm_token = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Firebase Cloud Messaging token for push notifications"
    )

    REQUIRED_FIELDS = ["phone_number"]
    objects = CustomUserManager()

    # def __str__(self) -> str:
    #     if self.user_type in (UserTypes.ADMIN, UserTypes.VENDOR):
    #         return self.username
    #     return self.phone_number

    class Meta:
        """
        the meta class
        """
        unique_together = ("username", "phone_number", "user_type")


class PhoneToken(models.Model):
    """
    phone token model
    """
    phone_number = models.CharField(
        max_length=20, validators=[is_valid_uzb_phone_number]
    )
    token = models.CharField(max_length=6, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now, null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    is_verified = models.BooleanField(default=False)

    class Meta:
        """
        the meta class
        """
        verbose_name = "Sms token"
        verbose_name_plural = "Sms Tokens"

    def __str__(self):
        return "{} - {}".format(self.phone_number, self.token)

    @property
    def is_expired(self):
        return self.expires_at < timezone.now()

    @classmethod
    def verify_token(cls, phone_number, token):
        try:
            phone_token = cls.objects.filter(phone_number=phone_number).last()
            if phone_token.token == token and phone_token.expires_at > timezone.now():
                return True
            return False
        except cls.DoesNotExist:
            return False

    @classmethod
    def generate_token(cls, phone_number):
        phone_token = cls.objects.create(phone_number=phone_number)
        token = "".join(random.choices(string.digits, k=5))
        phone_token.token = token
        phone_token.expires_at = timezone.now() + timezone.timedelta(minutes=3)
        phone_token.save()
        # message = "obihayot.uz platformasi uchun maxsus kod: {}".format(token)
        # # send_background_sms.apply_async((phone_number, message))
        # try:
        #     eskiz.send_sms(str(phone_number)[1:], message, from_whom="4546")
        # except:
        #     pass
        return phone_token.token


class Vendor(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="vendor")
    name = models.CharField(max_length=255)
    inn = models.CharField(max_length=255, null=True, blank=True)
    user_type = UserTypes.VENDOR
    logo = models.ImageField(
        blank=True,
        null=True,
        upload_to="vendors",
        validators=[FileExtensionValidator(allowed_extensions=['jpeg', 'jpg', 'png', 'webp'])])
    is_verified = models.BooleanField(default=False)
    objects = models.Manager()
    verified_verndors = VerifiedVendorManager()
    region = models.ForeignKey(
        "helpers.Region",
        on_delete=models.SET_NULL,
        null=True,
        related_name="vendors",
    )
    district = models.ForeignKey(
        "helpers.District",
        on_delete=models.SET_NULL,
        null=True,
        related_name="vendors",
    )
    total_bottles = models.IntegerField()
    description = models.TextField(blank=True, null=True)
    work_start_time = models.TimeField(null=True, blank=True, verbose_name="Ish boshlanish vaqti")
    work_end_time = models.TimeField(null=True, blank=True, verbose_name="Ish tugash vaqti")

    def __str__(self) -> str:
        return self.name

    class Meta:
        verbose_name = "Vendor"
        verbose_name_plural = "Vendors"
        ordering = ("-created_at",)

    @property
    def phone1(self):
        return self.user.phone_number

    @property
    def image_tag(self):
        try:
            return mark_safe(
                f"<img style='object-fit: cover; width: 30px; height: 30px;' src='{self.logo}'/>"
            )
        except:
            return ""


class Customer(BaseModel):
    fullname = models.CharField(max_length=120, null=True, blank=True)
    avatar = models.ImageField(
        blank=True,
        null=True,
        upload_to="customers",
        validators=[FileExtensionValidator(allowed_extensions=['jpeg', 'jpg', 'png', 'webp'])]
    )
    number2 = models.CharField(max_length=400, null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    latitude = models.FloatField(null=True, blank=True)
    passport = models.CharField(max_length=12, null=True, blank=True)
    region = models.ForeignKey(
        "helpers.Region",
        on_delete=models.SET_NULL,
        null=True,
        related_name="customers",
    )
    district = models.ForeignKey(
        "helpers.District",
        on_delete=models.SET_NULL,
        null=True,
        related_name="customers",
    )
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="customer")
    user_type = UserTypes.CUSTOMER

    def __str__(self) -> str:
        return self.fullname or self.user.phone_number or "Unknown Customer"

    @property
    def number1(self):
        return self.user.phone_number

    @property
    def image_tag(self):
        try:
            return mark_safe(
                f"<img style='object-fit: cover; width: 30px; height: 30px;' src='{self.avatar}'/>"
            )
        except:
            return ""

    class Meta:
        verbose_name = "Customer"
        verbose_name_plural = "Customers"
        ordering = ("-created_at",)


class Courier(BaseModel):
    """
    The courier model
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="courier")
    vendors = models.ManyToManyField(
        "users.Vendor",
        related_name="couriers",
        blank=True,
    )
    fullname = models.CharField(max_length=255, verbose_name="FIO")
    address = models.CharField(max_length=255, verbose_name="Address", null=True, blank=True)

    objects = CourierManager()

    user_type = UserTypes.COURIER

    class Meta:
        """
        the meta class
        """
        verbose_name = "Courier"
        verbose_name_plural = "Couriers"
        ordering = ['-created_at']

    def __str__(self):
        return self.fullname or self.user.phone_number

    def save(self, *args, **kwargs):
        if self.user and self.user.user_type != self.user_type:
            self.user.user_type = self.user_type
            self.user.save()
        super().save(*args, **kwargs)
