"""
the bot register view
"""
import logging

from django.db import transaction, IntegrityError
from django.shortcuts import get_object_or_404
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken

from apps.bot.models import TelegramUser
from apps.users.customers.serializers import CustomerCreateSerializer

# Configure logging
logger = logging.getLogger(__name__)

class MiniAppRegistrationView(APIView):
    """
    Secure API endpoint to handle Telegram Mini App registration form submission
    using chat_id for authentication.
    """
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        """
        Process registration from Telegram Mini App.
        """
        # Get chat_id from query parameter
        chat_id = request.query_params.get("chat_id")

        if not chat_id:
            return Response(
                {"error": "Missing required chat_id parameter"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Convert chat_id to integer
            try:
                chat_id = int(chat_id)
            except ValueError:
                return Response(
                    {"error": "Invalid chat_id format"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Find corresponding Telegram user
            telegram_user = get_object_or_404(
                TelegramUser.objects.select_related('user'),
                chat_id=chat_id
            )

            # Validate and create customer
            serializer = CustomerCreateSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            customer = serializer.save()

            # Activate the user after creation
            user = customer.user
            if not user.is_active:
                user.is_active = True
                user.save()

            # Link customer to Telegram user and mark as registered
            telegram_user.link_to_user(customer)
            telegram_user.is_registered = True
            telegram_user.save(update_fields=['is_registered'])

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "message": "Registration successful"
                },
                status=status.HTTP_201_CREATED
            )
        except ValidationError as ve:
            logger.error(f"Validation error: {str(ve)}")
            return Response(
                {"error": "Validation error", "details": str(ve)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except ObjectDoesNotExist as ode:
            logger.error(f"Object not found: {str(ode)}")
            return Response(
                {"error": "Object not found", "details": str(ode)},
                status=status.HTTP_404_NOT_FOUND
            )
        except IntegrityError as ie:
            logger.error(f"Database integrity error: {str(ie)}")
            return Response(
                {"error": "Database error", "details": str(ie)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return Response(
                {"error": "An unexpected error occurred", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
