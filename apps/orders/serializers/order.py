"""
order serializers
"""
from rest_framework import serializers
from django.core.exceptions import ObjectDoesNotExist

from apps.orders.models.order import Order, OrderStatus
from apps.orders.models.application import OrderApplication, ApplicationStatus
from apps.users.models import Customer, UserTypes
from apps.users.customers.serializers import CustomerSerializer
from apps.products.models import Product
from apps.users.vendors.models import VendorClient
from apps.helpers.serializers import RegionSerializer, DistrictSerializer


class OrderCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating orders
    """

    customer = serializers.CharField(required=False)
    phone_number = serializers.CharField(required=False, allow_blank=True)
    fullname = serializers.CharField(required=False)
    is_first_order = serializers.SerializerMethodField()

    class Meta:
        """
        the meta field
        """
        model = Order
        fields = (
            "id", "customer", "vendor", "product", "fullname", "quantity", "status",
            "deliverable_time", "returned_bottles", "total_returned_bottles", "region", "district",
            "address", "longitude", "latitude", "phone_number", "additional_number",
            "passport", "total_price", "created_at", "updated_at", "delivered_at", "is_first_order"
        )
        read_only_fields = (
            "id", "delivered_at", "total_price", "created_at",
            "updated_at", "total_returned_bottles", "is_first_order"
        )

    def create(self, validated_data):
        """
        Create order based on user type.
        """
        user = self.context["request"].user

        if user.user_type == UserTypes.CUSTOMER:
            return self._create_customer_order(user, validated_data)
        elif user.user_type == UserTypes.VENDOR:
            return self._create_vendor_order(validated_data)

        raise serializers.ValidationError(
            {"message": "You do not have permission to create orders"}
        )

    def _create_customer_order(self, user, validated_data):
        """
        Create order for customer users, keeping status as PENDING.
        """
        from apps.users.vendors.models import VendorClient

        try:
            customer = user.customer
        except ObjectDoesNotExist as exc:
            raise serializers.ValidationError(
                {"message": "Your account is not properly set up as a customer. Please contact support."}
            ) from exc

        validated_data["customer"] = customer
        validated_data["phone_number"] = user.phone_number

        self._update_customer_info(customer, validated_data)

        # Get vendor from product
        product = validated_data.get("product")
        vendor = product.vendor if product else None
        if not vendor:
            raise serializers.ValidationError({"message": "The selected product is not available."})

        # Create the order with PENDING status
        validated_data["status"] = OrderStatus.PENDING
        order = super().create(validated_data)

        # Create or update VendorClient record
        VendorClient.objects.get_or_create(
            vendor=vendor,
            customer=customer,
            defaults={
                "phone_number": user.phone_number,
                "fullname": customer.fullname,
                "address": validated_data.get("address", ""),
                "region": validated_data.get("region"),
                "district": validated_data.get("district"),
                "passport": validated_data.get("passport", ""),
                "additional_number": validated_data.get("additional_number", ""),
            }
        )

        return order

    def _create_vendor_order(self, validated_data):
        """
        Create order for vendor users with ACCEPTED status.
        """
        from apps.users.vendors.models import VendorClient

        try:
            customer_id = validated_data.pop("customer", None)
            customer = Customer.objects.get(id=customer_id) if customer_id else None
            validated_data["customer"] = customer

            if customer:
                validated_data["phone_number"] = customer.user.phone_number
        except Customer.DoesNotExist as exc:
            raise serializers.ValidationError({"message": "Customer not found"}) from exc

        # Temporarily set to PENDING to allow creation
        validated_data["status"] = OrderStatus.PENDING
        order = super().create(validated_data)

        # Immediately update to ACCEPTED
        order.status = OrderStatus.ACCEPTED
        order.save()

        # Add to VendorClient if not present
        if customer:
            vendor = validated_data.get("product").vendor if validated_data.get("product") else None
            if vendor:
                VendorClient.objects.get_or_create(
                    vendor=vendor,
                    customer=customer,
                    defaults={
                        "phone_number": customer.user.phone_number,
                        "fullname": customer.fullname,
                        "address": validated_data.get("address", ""),
                        "region": validated_data.get("region"),
                        "district": validated_data.get("district"),
                        "passport": validated_data.get("passport", ""),
                        "additional_number": validated_data.get("additional_number", ""),
                    }
                )

        return order

    def _update_customer_info(self, customer, validated_data):
        """
        Update customer additional info if provided.
        """
        additional_number = validated_data.get("additional_number")
        passport = validated_data.get("passport")

        update_needed = False

        if additional_number:
            customer.number2 = additional_number
            update_needed = True

        if passport:
            customer.passport = passport
            update_needed = True

        if update_needed:
            customer.save()

    def get_is_first_order(self, obj):
        """
        Removed logic from serializer to rely on model logic.
        """
        return obj.is_first_order

    def validate(self, attrs):
        """
        Validate order data.
        """
        user = self.context["request"].user

        if user.user_type in [UserTypes.VENDOR, UserTypes.COURIER]:
            self._validate_vendor_product(attrs)

        return super().validate(attrs)

    def _validate_vendor_product(self, attrs):
        """
        Validate vendor owns the product.
        """
        user = self.context["request"].user

        try:
            product = self.instance.product if self.instance else attrs["product"]
        except (AttributeError, KeyError):
            raise serializers.ValidationError({"message": "Product is required"})

        if product.vendor != user.vendor:
            raise serializers.ValidationError(
                {"message": "You can only handle orders for your own products"}
            )

    def update(self, instance, validated_data):
        """
        Update an order.
        """
        user = self.context["request"].user

        if user.user_type == UserTypes.VENDOR:
            self._validate_vendor_ownership(instance)
        elif user.user_type == UserTypes.COURIER:
            self._validate_courier_permissions(instance, validated_data)
        else:
            raise serializers.ValidationError(
                {"message": "You do not have permission to update orders"}
            )

        return super().update(instance, validated_data)

    def _validate_vendor_ownership(self, instance):
        """
        Validate vendor owns the order's product.
        """
        user = self.context["request"].user

        if instance.product.vendor != user.vendor:
            raise serializers.ValidationError(
                {"message": "You can only update orders for your own products"}
            )

    def _validate_courier_permissions(self, instance, validated_data):
        """
        Validate courier permissions for updating an order
        """
        user = self.context["request"].user

        # Allow courier to assign order to themselves if status is ORDER_ACCEPTED and courier is None
        if instance.courier != user:
            is_valid = (instance.status == OrderStatus.ACCEPTED and
                      instance.courier is None and
                      user.user_type == UserTypes.COURIER)
            if not is_valid:
                raise serializers.ValidationError(
                    {"message": "You can only update your assigned orders"}
                )

        allowed_fields = ["status", "returned_bottles", "total_returned_bottles", "delivered_at"]
        disallowed_fields = [field for field in validated_data if field not in allowed_fields]

        if disallowed_fields:
            raise serializers.ValidationError({
                "message": f"As a courier, you can only update: {', '.join(allowed_fields)}"
            })


class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer for Product model in Order contexts.
    """
    class Meta:
        """
        the meta class
        """
        model = Product
        fields = ("id", "title", "price")


class OrderSerializer(serializers.ModelSerializer):
    """
    Serializer for reading Order instances.
    """
    customer = CustomerSerializer(read_only=True)
    phone_number = serializers.CharField(required=False, allow_blank=True)
    region = RegionSerializer()
    district = DistrictSerializer()
    product = ProductSerializer()
    vendor = serializers.SerializerMethodField()
    courier = serializers.SerializerMethodField()
    is_first_order = serializers.SerializerMethodField()

    class Meta:
        """
        the meta class
        """
        model = Order
        fields = (
            "id", "customer", "product", "vendor", "courier", "quantity", "status", "returned_bottles",
            "total_returned_bottles", "region", "district", "address", "longitude",
            "latitude", "phone_number", "additional_number", "passport", "total_price",
            "created_at", "updated_at", "delivered_at", "deliverable_time", "is_first_order",
        )
        read_only_fields = fields

    def get_vendor(self, obj):
        """
        get vendor specific fields
        """
        if obj.vendor:
            vendor_data = {
                "id": obj.vendor.id,
                "name": obj.vendor.name,
            }

            # Try to get phone number from the user associated with vendor
            if hasattr(obj.vendor, 'user') and hasattr(obj.vendor.user, 'phone_number'):
                vendor_data["phone_number"] = obj.vendor.user.phone_number

            return vendor_data
        return None

    def get_courier(self, obj):
        """
        get courier specific fields
        """
        if obj.courier:
            courier_data = {
                "id": obj.courier.id,
                "fullname": obj.courier.fullname,
            }
            return courier_data
        return None

    def get_is_first_order(self, obj):
        """
        Removed logic from serializer to rely on model logic.
        """
        return obj.is_first_order

    def to_representation(self, instance):
        """
        Customize order representation.
        """
        data = super().to_representation(instance)
        customer = instance.customer

        if not customer:
            data["customer"] = {}
            try:
                data["customer"]["fullname"] = instance.fullname
            except AttributeError:
                pass

            try:
                data["customer"]["passport"] = data["passport"]
            except KeyError:
                pass

        return data
