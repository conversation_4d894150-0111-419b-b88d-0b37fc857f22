"""
order viewset
"""
from django.utils.dateparse import parse_datetime
from django.db import IntegrityError
from django.db.models import Count
from django_filters.rest_framework import DjangoFilterBackend
from django.core.exceptions import ValidationError
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from rest_framework import viewsets, filters, permissions
from rest_framework import status, serializers
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.exceptions import PermissionDenied

from apps.users.models import Customer, UserTypes
from apps.orders.models import Order, OrderStatus
from apps.orders.serializers import OrderCreateSerializer
from apps.orders.serializers import OrderSerializer, OrderStatusChangeSerializer
from apps.orders.filters import OrderFilter


@method_decorator(csrf_exempt, name='dispatch')
class OrderViewSet(viewsets.ModelViewSet):
    """
    The order view
    """
    queryset = Order.objects.all().order_by("-updated_at")
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    http_method_names = ["get", "post", "patch", "delete"]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ["customer__fullname", "fullname", "address", "quantity", "id"]
    filterset_class = OrderFilter

    def get_queryset(self):
        user = self.request.user
        status_filter = self.request.query_params.get('status')
        base_queryset = self.queryset

        # Filter by status group if specified
        if status_filter in ['active', 'archived']:
            from apps.orders.models.order import get_status_group
            statuses = get_status_group(status_filter)
            base_queryset = base_queryset.filter(status__in=statuses)

        if not user.is_authenticated:
            return base_queryset.none()

        # Get all pending applications
        from apps.orders.models.application import OrderApplication
        pending_application_orders = OrderApplication.objects.filter(status='pending')
        base_queryset = base_queryset.exclude(id__in=pending_application_orders)

        # Filter based on user type
        if user.user_type == UserTypes.VENDOR:
            return base_queryset.filter(vendor=user.vendor)

        if user.user_type == UserTypes.COURIER:
            try:
                courier = user.courier
                if courier.vendors.exists():
                    return base_queryset.filter(vendor__in=courier.vendors.all())
                return base_queryset.none()
            except Exception:
                return base_queryset.none()

        if user.user_type == UserTypes.CUSTOMER:
            try:
                customer = user.customer
            except Customer.DoesNotExist:
                return base_queryset.none()

            # Get customer orders that have either been accepted or are from repeat orders
            return base_queryset.filter(customer=customer)

        if user.user_type == UserTypes.ADMIN:
            return base_queryset

        raise PermissionDenied("You don't have permission to access")

    def get_serializer_class(self):
        if self.request.method in ["POST", "PATCH"]:
            return OrderCreateSerializer
        return OrderSerializer

    def perform_destroy(self, instance):
        request_user = self.request.user
        if request_user.user_type == UserTypes.CUSTOMER:
            if request_user.customer != instance.customer:
                raise serializers.ValidationError(
                    {"detail": "You don't have permission to perform this action."}
                )
            instance.delete()
            return Response(
                {"message": "Order has been deleted"},
                status=status.HTTP_204_NO_CONTENT,
            )
        raise serializers.ValidationError(
            {"detail": "You don't have permission to perform this action."}
        )

    def filter_by_date_range(self, queryset, request):
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if start_date:
            start_date = parse_datetime(start_date)
            if start_date:
                queryset = queryset.filter(created_at__gte=start_date)

        if end_date:
            end_date = parse_datetime(end_date)
            if end_date:
                queryset = queryset.filter(created_at__lte=end_date)

        return queryset

    @action(detail=False, methods=["get"])
    def orders_count(self, request):
        queryset = self.filter_by_date_range(Order.objects.all(), request)
        total_orders = queryset.count()
        return Response({"total_orders": total_orders})

    @action(detail=False, methods=["get"])
    def vendor_orders_count(self, request):
        queryset = self.filter_by_date_range(Order.objects.all(), request)
        vendor_counts = queryset.values("vendor__name") \
            .annotate(total_orders=Count("id"))
        return Response({"vendor_orders": vendor_counts})

    def create(self, request, *args, **kwargs):
        """
        Create a new order with improved error handling
        """
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            order = serializer.save()

            # Return full order details using OrderSerializer
            return Response(OrderSerializer(order).data, status=status.HTTP_201_CREATED)

        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": e.detail.get("message", "Invalid data provided"),
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while creating the order",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['patch'])
    def change_status(self, request, pk=None):
        """
        Change the status of an order with improved error handling
        """
        order = self.get_object()
        serializer = OrderStatusChangeSerializer(
            data=request.data,
            context={'request': request, 'order': order}
        )

        if serializer.is_valid():
            try:
                new_status = serializer.validated_data['status']

                # Prevent changing the status from DONE to DONE
                if order.status == OrderStatus.FINISHED and new_status == OrderStatus.FINISHED:
                    return Response(
                        {
                            "status": "error",
                            "message": "Order is already completed"
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Additional check for CANCELLED status
                if new_status == OrderStatus.CANCELLED and request.user.user_type != UserTypes.CUSTOMER:
                    return Response(
                        {
                            "status": "error",
                            "message": "Only customers can cancel their own orders"
                        },
                        status=status.HTTP_403_FORBIDDEN
                    )

                delivered_quantity = serializer.validated_data.get('delivered_quantity')
                returned_bottles = serializer.validated_data.get('returned_bottles')

                # Update order status and quantities if status is DONE
                if new_status == OrderStatus.FINISHED:
                    order.set_status_finished(
                        delivered_quantity=delivered_quantity,
                        returned_bottles=returned_bottles
                    )
                else:
                    order.status = new_status
                    order.save()

                # Return full order details
                order_serializer = OrderSerializer(order)
                return Response(
                    order_serializer.data,
                    status=status.HTTP_200_OK
                )
            except ValidationError as ve:
                return Response(
                    {
                        "status": "error",
                        "message": "Invalid data provided",
                        "details": str(ve)
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
            except Exception as e:
                return Response(
                    {
                        "status": "error",
                        "message": "An unexpected error occurred while updating the order status",
                        "details": str(e)
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        return Response(
            {
                "status": "error",
                "message": "Invalid data provided",
                "details": serializer.errors
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=["patch"])
    def mark_delivered(self, request, pk=None):
        """
        Mark order as delivered.
        Optionally accepts delivered_quantity and returned_bottles.
        """
        order = self.get_object()
        user = request.user

        delivered_quantity = request.data.get("delivered_quantity")
        returned_bottles = request.data.get("returned_bottles", 0)

        if user.user_type not in [UserTypes.VENDOR, UserTypes.ADMIN]:
            return Response(
                {
                    "status": "error",
                    "message": "You don't have permission to mark delivered.",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            order.set_status_finished(
                delivered_quantity=delivered_quantity,
                returned_bottles=returned_bottles,
            )
        except ValidationError as ve:
            return Response(
                {"status": "error", "message": "Validation error", "details": str(ve)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except IntegrityError as ie:
            return Response(
                {"status": "error", "message": "Database error", "details": str(ie)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as exc:
            return Response(
                {"status": "error", "message": "An unexpected error occurred", "details": str(exc)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # Return full order details
        order_serializer = OrderSerializer(order)
        return Response(
            order_serializer.data,
            status=status.HTTP_200_OK
        )
