"""
Signal handlers for notifications.
"""
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model

from apps.orders.models import Order, OrderApplication
from apps.notifications.services import FirebaseService
from apps.notifications.models import Notification

User = get_user_model()


class NotificationHelper:
    """
    Helper class for consistent notification handling.
    """

    @staticmethod
    def send_notification(user, title, body, notification_type, data):
        """
        Send a basic notification.
        """
        if not user or not user.fcm_token:
            return False

        try:
            # Create database record
            Notification.objects.create(
                user=user,
                title=title,
                body=body,
                notification_type=notification_type,
                data=data
            )

            # Send FCM notification
            registration_ids = [user.fcm_token] if isinstance(user.fcm_token, str) else user.fcm_token
            FirebaseService().send_notification(
                registration_ids,
                title,
                body,
                data
            )
            return True

        except Exception as e:
            print(f"Notification error: {e}")
            return False


@receiver(post_save, sender=Order)
def handle_order_created(sender, instance, created, **kwargs):
    """
    Notify vendor when new order is created.
    """
    if created and instance.vendor and instance.vendor.user:
        vendor_user = instance.vendor.user
        NotificationHelper.send_notification(
            user=vendor_user,
            title="New Order",
            body=f"You have received a new order #{instance.id}",
            notification_type="new_order",
            data={
                "order_id": str(instance.id),
                "type": "new_order"
            }
        )
        FirebaseService.update_notification_counts(
            str(vendor_user.id),
            'orders',
            1
        )


@receiver(post_save, sender=OrderApplication)
def handle_application_created(sender, instance, created, **kwargs):
    """
    Notify vendor when new application is created
    """
    if created and instance.order and instance.order.vendor and instance.order.vendor.user:
        vendor_user = instance.order.vendor.user
        NotificationHelper.send_notification(
            user=vendor_user,
            title="New Application",
            body=f"New application for order #{instance.order.id}",
            notification_type="new_application",
            data={
                "order_id": str(instance.order.id),
                "application_id": str(instance.id),
                "type": "new_application"
            }
        )
        FirebaseService.update_notification_counts(
            str(vendor_user.id),
            'applications',
            1
        )
